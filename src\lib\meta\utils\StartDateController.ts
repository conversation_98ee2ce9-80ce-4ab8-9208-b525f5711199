/**
 * Start Date Controller - Smart date input logic for employment start date fields
 * Handles arrow key navigation, business constraints, and smart initialization
 */

export interface StartDateConstraints {
  minYear: number;  // 2015 (company founding year)
  maxYear: number;  // currentYear + 10 (planning horizon)
  currentYear: number;
  companyFoundingYear: number;
  planningHorizonYears: number;
}

export class StartDateController {
  private constraints: StartDateConstraints;

  constructor() {
    const currentYear = new Date().getFullYear();
    const companyFoundingYear = 2015;
    const planningHorizonYears = 10;
    
    this.constraints = {
      minYear: companyFoundingYear,  // Company was founded in 2015
      maxYear: currentYear + planningHorizonYears,  // 10 years into future
      currentYear,
      companyFoundingYear,
      planningHorizonYears
    };
  }

  /**
   * Handles arrow key navigation for day, month, and year fields
   * @param key - The pressed key ('ArrowUp' or 'ArrowDown')
   * @param currentValue - Current input value
   * @param cursorPosition - Current cursor position in input
   * @returns New date string or null if no change needed
   */
  handleArrowKeyNavigation(
    key: string,
    currentValue: string,
    cursorPosition: number
  ): string | null {
    // Only handle arrow keys
    if (!['ArrowUp', 'ArrowDown'].includes(key)) {
      return null;
    }

    // Determine which field the cursor is in
    const fieldType = this.getCursorFieldType(currentValue, cursorPosition);

    if (!fieldType) {
      return null;
    }

    // If field is empty, initialize with smart default (today or next Monday)
    if (!currentValue || currentValue === '') {
      const smartDefault = this.getSmartDefaultDate();
      return this.formatDateForInput(smartDefault);
    }

    // Parse current date
    const currentDate = this.parseInputValue(currentValue);
    if (!currentDate) {
      // If parsing fails, initialize with smart default
      const smartDefault = this.getSmartDefaultDate();
      return this.formatDateForInput(smartDefault);
    }

    // Navigate based on field type
    const newDate = this.navigateField(currentDate, fieldType, key);
    if (!newDate) {
      return null;
    }

    return this.formatDateForInput(newDate);
  }

  /**
   * Validates and corrects a date input
   * @param inputValue - The input value to validate
   * @returns Corrected date string or original if valid
   */
  validateAndCorrect(inputValue: string): string {
    if (!inputValue) return inputValue;

    const date = this.parseInputValue(inputValue);
    if (!date) return inputValue;

    const year = date.getFullYear();
    
    // Clamp year to valid business range
    const correctedYear = Math.max(
      this.constraints.minYear,
      Math.min(year, this.constraints.maxYear)
    );

    if (correctedYear !== year) {
      const correctedDate = new Date(date);
      correctedDate.setFullYear(correctedYear);
      return this.formatDateForInput(correctedDate);
    }

    return inputValue;
  }

  /**
   * Gets HTML attributes for the input field
   */
  getHTMLAttributes() {
    return {
      min: `${this.constraints.minYear}-01-01`,
      max: `${this.constraints.maxYear}-12-31`,
      placeholder: 'mm/dd/yyyy'
    };
  }

  /**
   * Gets smart default date for start date (today or next Monday)
   */
  private getSmartDefaultDate(): Date {
    const today = new Date();
    const dayOfWeek = today.getDay(); // 0 = Sunday, 1 = Monday, etc.
    
    // If it's Friday (5), Saturday (6), or Sunday (0), suggest next Monday
    if (dayOfWeek === 0 || dayOfWeek === 5 || dayOfWeek === 6) {
      const daysUntilMonday = dayOfWeek === 0 ? 1 : (8 - dayOfWeek);
      const nextMonday = new Date(today);
      nextMonday.setDate(today.getDate() + daysUntilMonday);
      return nextMonday;
    }
    
    // Otherwise, suggest today
    return today;
  }

  /**
   * Determines which field (day, month, year) the cursor is positioned in
   * For HTML5 date inputs in format YYYY-MM-DD
   */
  private getCursorFieldType(value: string, cursorPosition: number): 'day' | 'month' | 'year' | null {
    if (!value || value.length < 10) {
      // For incomplete dates, allow navigation on any position
      return 'year'; // Default to year for initialization
    }

    // HTML5 date input format: YYYY-MM-DD (positions 0-9)
    // Year: positions 0-3
    // Month: positions 5-6
    // Day: positions 8-9

    if (cursorPosition >= 0 && cursorPosition <= 4) {
      return 'year';
    } else if (cursorPosition >= 5 && cursorPosition <= 7) {
      return 'month';
    } else if (cursorPosition >= 8 && cursorPosition <= 10) {
      return 'day';
    }

    return null;
  }

  /**
   * Navigates a specific field (day, month, year) up or down
   */
  private navigateField(currentDate: Date, fieldType: 'day' | 'month' | 'year', key: string): Date | null {
    const newDate = new Date(currentDate);
    const isUp = key === 'ArrowUp';

    switch (fieldType) {
      case 'day':
        return this.navigateDay(newDate, isUp);
      case 'month':
        return this.navigateMonth(newDate, isUp);
      case 'year':
        return this.navigateYear(newDate, isUp);
      default:
        return null;
    }
  }

  /**
   * Navigate day field with proper month boundaries
   */
  private navigateDay(date: Date, isUp: boolean): Date {
    const currentDay = date.getDate();
    const currentMonth = date.getMonth();
    const currentYear = date.getFullYear();

    if (isUp) {
      // Get last day of current month
      const lastDayOfMonth = new Date(currentYear, currentMonth + 1, 0).getDate();
      const newDay = currentDay < lastDayOfMonth ? currentDay + 1 : 1;
      date.setDate(newDay);
    } else {
      const newDay = currentDay > 1 ? currentDay - 1 : new Date(currentYear, currentMonth + 1, 0).getDate();
      date.setDate(newDay);
    }

    return date;
  }

  /**
   * Navigate month field with year boundaries
   */
  private navigateMonth(date: Date, isUp: boolean): Date {
    const currentMonth = date.getMonth();

    if (isUp) {
      const newMonth = currentMonth < 11 ? currentMonth + 1 : 0;
      if (newMonth === 0) {
        // Moving to January of next year
        const newYear = Math.min(date.getFullYear() + 1, this.constraints.maxYear);
        date.setFullYear(newYear);
      }
      date.setMonth(newMonth);
    } else {
      const newMonth = currentMonth > 0 ? currentMonth - 1 : 11;
      if (newMonth === 11) {
        // Moving to December of previous year
        const newYear = Math.max(date.getFullYear() - 1, this.constraints.minYear);
        date.setFullYear(newYear);
      }
      date.setMonth(newMonth);
    }

    // Adjust day if it doesn't exist in the new month
    const lastDayOfNewMonth = new Date(date.getFullYear(), date.getMonth() + 1, 0).getDate();
    if (date.getDate() > lastDayOfNewMonth) {
      date.setDate(lastDayOfNewMonth);
    }

    return date;
  }

  /**
   * Navigate year field with constraints
   */
  private navigateYear(date: Date, isUp: boolean): Date {
    const currentYear = date.getFullYear();
    const newYear = isUp
      ? Math.min(currentYear + 1, this.constraints.maxYear)
      : Math.max(currentYear - 1, this.constraints.minYear);

    date.setFullYear(newYear);

    // Adjust day if it doesn't exist in the new year (Feb 29 edge case)
    const lastDayOfMonth = new Date(newYear, date.getMonth() + 1, 0).getDate();
    if (date.getDate() > lastDayOfMonth) {
      date.setDate(lastDayOfMonth);
    }

    return date;
  }

  /**
   * Parses input value to Date object
   */
  private parseInputValue(value: string): Date | null {
    if (!value) return null;
    
    const date = new Date(value);
    return isNaN(date.getTime()) ? null : date;
  }

  /**
   * Formats date for HTML date input (yyyy-mm-dd)
   */
  private formatDateForInput(date: Date): string {
    return date.toISOString().split('T')[0];
  }

  /**
   * Gets current constraints
   */
  getConstraints(): StartDateConstraints {
    return { ...this.constraints };
  }
}
