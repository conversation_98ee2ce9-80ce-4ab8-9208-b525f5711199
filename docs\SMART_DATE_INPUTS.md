# Smart Date Input Components

## Overview

This document describes the implementation of smart date input components for the Ringerike Landskap website's arbeidskontrakt generator. These components provide enhanced keyboard navigation and intelligent defaults for birth date and start date fields.

## Architecture

### Component Structure

```
src/ui/Form/
├── SmartBirthDateInput/
│   └── index.tsx
└── SmartStartDateInput/
    └── index.tsx
```

### Supporting Infrastructure

```
src/lib/
├── hooks/
│   ├── useSmartBirthDate.ts
│   └── useSmartStartDate.ts
└── meta/utils/
    ├── BirthDateController.ts
    ├── StartDateController.ts
    └── index.ts
```

## Implementation Details

### Controllers

**BirthDateController**
- Manages birth date constraints (1925-2010)
- Provides smart initialization (reasonable adult age)
- Handles validation and correction

**StartDateController**
- Manages start date constraints (2015-current+10)
- Provides smart initialization (today or next Monday)
- Handles business logic for employment dates

### Hooks

**useSmartBirthDate**
- Integrates BirthDateController with React state
- Handles keyboard events and validation
- Manages ref forwarding

**useSmartStartDate**
- Integrates StartDateController with React state
- Handles keyboard events and validation
- Manages ref forwarding

### Components

**SmartBirthDateInput**
- HTML5 date input with enhanced UX
- Keyboard navigation support
- Validation and error handling
- Accessibility features

**SmartStartDateInput**
- HTML5 date input with business logic
- Smart defaults for work schedules
- Validation and error handling
- Accessibility features

## Usage

```tsx
import { SmartBirthDateInput, SmartStartDateInput } from '@/ui/Form';

// Birth date input
<SmartBirthDateInput
  label="Fødselsdato"
  value={formData.employeeBirthDate}
  onChange={(value) => updateFormData({ employeeBirthDate: value })}
  required
/>

// Start date input
<SmartStartDateInput
  label="Startdato"
  value={formData.startDate}
  onChange={(value) => updateFormData({ startDate: value })}
  required
/>
```

## Features

### Keyboard Navigation
- Native browser navigation for day/month/year fields
- Smart initialization when field is empty
- Constraint enforcement within valid ranges

### Smart Defaults
- **Birth Date**: Initializes to reasonable adult age (~30 years old)
- **Start Date**: Initializes to today or next Monday (business logic)

### Validation
- Automatic constraint enforcement
- Real-time validation feedback
- Error handling and correction

### Accessibility
- Proper ARIA labels and descriptions
- Keyboard navigation support
- Screen reader compatibility

## Constraints

### Birth Date Constraints
- **Minimum Year**: 1925 (100 years maximum age)
- **Maximum Year**: 2010 (15 years minimum age)
- **Default**: Current year - 30 (reasonable adult age)

### Start Date Constraints
- **Minimum Year**: 2015 (company founding year)
- **Maximum Year**: Current year + 10 (planning horizon)
- **Default**: Today or next Monday (business logic)

## SEO Considerations

- Semantic HTML5 date inputs
- Proper form labeling
- Accessible error messaging
- Progressive enhancement

## Maintenance Notes

### Adding New Date Input Types
1. Create new controller in `src/lib/meta/utils/`
2. Create corresponding hook in `src/lib/hooks/`
3. Create UI component in `src/ui/Form/ComponentName/`
4. Export from respective index files
5. Update documentation

### Modifying Constraints
- Update controller constructor values
- Ensure validation logic remains consistent
- Test edge cases (leap years, month boundaries)
- Update documentation and tip text

## Related Documentation

- [Form Components](../src/ui/Form/README.md)
- [Meta Utilities](../src/lib/meta/README.md)
- [Hooks Documentation](../src/lib/hooks/README.md)
