/**
 * Birth Date Controller - Smart date input logic for birth date fields
 * Handles arrow key navigation, year constraints, and smart initialization
 */

export interface BirthDateConstraints {
  minYear: number;  // 1925 (current year - 100)
  maxYear: number;  // 2010 (current year - 15)
  currentYear: number;
}

export class BirthDateController {
  private constraints: BirthDateConstraints;

  constructor() {
    const currentYear = new Date().getFullYear();
    this.constraints = {
      minYear: 1925,  // Fixed minimum (100 years max age)
      maxYear: 2010,  // Fixed maximum (15 years min age)
      currentYear
    };
  }

  /**
   * Handles arrow key navigation for date fields
   * @param key - The pressed key ('ArrowUp' or 'ArrowDown')
   * @param currentValue - Current input value
   * @param cursorPosition - Current cursor position in input
   * @returns New date string or null if no change needed
   */
  handleArrowKeyNavigation(
    key: string,
    currentValue: string,
    _cursorPosition: number
  ): string | null {
    // Only handle arrow keys
    if (!['ArrowUp', 'ArrowDown'].includes(key)) {
      return null;
    }

    // For HTML5 date inputs, let the browser handle all navigation natively
    // This allows proper day/month/year navigation without interference
    // We only intercept when the field is empty to provide smart initialization

    if (!currentValue || currentValue === '') {
      // Initialize empty field with a reasonable default
      const initialYear = this.constraints.currentYear;
      return this.createDateStringWithYear(initialYear);
    }

    // For non-empty fields, let browser handle navigation natively
    return null;
  }

  /**
   * Validates and corrects a date input
   * @param inputValue - The input value to validate
   * @returns Corrected date string or original if valid
   */
  validateAndCorrect(inputValue: string): string {
    if (!inputValue) return inputValue;

    const date = this.parseInputValue(inputValue);
    if (!date) return inputValue;

    const year = date.getFullYear();
    
    // Clamp year to valid range
    const correctedYear = Math.max(
      this.constraints.minYear,
      Math.min(year, this.constraints.maxYear)
    );

    if (correctedYear !== year) {
      const correctedDate = new Date(date);
      correctedDate.setFullYear(correctedYear);
      return this.formatDateForInput(correctedDate);
    }

    return inputValue;
  }

  /**
   * Gets HTML attributes for the input field
   */
  getHTMLAttributes() {
    return {
      min: `${this.constraints.minYear}-01-01`,
      max: `${this.constraints.maxYear}-12-31`,
      placeholder: 'mm/dd/yyyy'
    };
  }



  /**
   * Parses input value to Date object
   */
  private parseInputValue(value: string): Date | null {
    if (!value) return null;
    
    const date = new Date(value);
    return isNaN(date.getTime()) ? null : date;
  }

  /**
   * Creates a date string with specified year (always January 1st)
   */
  private createDateStringWithYear(year: number): string {
    // Create date string directly to avoid timezone issues
    // Format: YYYY-01-01 (January 1st)
    return `${year.toString().padStart(4, '0')}-01-01`;
  }

  /**
   * Formats date for HTML date input (yyyy-mm-dd)
   */
  private formatDateForInput(date: Date): string {
    return date.toISOString().split('T')[0];
  }

  /**
   * Gets current constraints
   */
  getConstraints(): BirthDateConstraints {
    return { ...this.constraints };
  }
}
