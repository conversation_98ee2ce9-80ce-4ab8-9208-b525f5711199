/**
 * Birth Date Controller - Smart date input logic for birth date fields
 * Handles arrow key navigation, year constraints, and smart initialization
 */

export interface BirthDateConstraints {
  minYear: number;  // 1925 (current year - 100)
  maxYear: number;  // 2010 (current year - 15)
  currentYear: number;
}

export class BirthDateController {
  private constraints: BirthDateConstraints;

  constructor() {
    const currentYear = new Date().getFullYear();
    this.constraints = {
      minYear: 1925,  // Fixed minimum (100 years max age)
      maxYear: 2010,  // Fixed maximum (15 years min age)
      currentYear
    };
  }

  /**
   * Handles arrow key navigation for day, month, and year fields
   * @param key - The pressed key ('ArrowUp' or 'ArrowDown')
   * @param currentValue - Current input value
   * @param cursorPosition - Current cursor position in input
   * @returns New date string or null if no change needed
   */
  handleArrowKeyNavigation(
    key: string,
    currentValue: string,
    cursorPosition: number
  ): string | null {
    // Only handle arrow keys
    if (!['ArrowUp', 'ArrowDown'].includes(key)) {
      return null;
    }

    // Determine which field the cursor is in
    const fieldType = this.getCursorFieldType(currentValue, cursorPosition);

    if (!fieldType) {
      return null;
    }

    // If field is empty, initialize with January 1st of current year
    if (!currentValue || currentValue === '') {
      const initialYear = this.constraints.currentYear;
      return this.createDateStringWithYear(initialYear);
    }

    // Parse current date
    const currentDate = this.parseInputValue(currentValue);
    if (!currentDate) {
      // If parsing fails, initialize with January 1st of current year
      const initialYear = this.constraints.currentYear;
      return this.createDateStringWithYear(initialYear);
    }

    // Navigate based on field type
    const newDate = this.navigateField(currentDate, fieldType, key);
    if (!newDate) {
      return null;
    }

    return this.formatDateForInput(newDate);
  }

  /**
   * Validates and corrects a date input
   * @param inputValue - The input value to validate
   * @returns Corrected date string or original if valid
   */
  validateAndCorrect(inputValue: string): string {
    if (!inputValue) return inputValue;

    const date = this.parseInputValue(inputValue);
    if (!date) return inputValue;

    const year = date.getFullYear();
    
    // Clamp year to valid range
    const correctedYear = Math.max(
      this.constraints.minYear,
      Math.min(year, this.constraints.maxYear)
    );

    if (correctedYear !== year) {
      const correctedDate = new Date(date);
      correctedDate.setFullYear(correctedYear);
      return this.formatDateForInput(correctedDate);
    }

    return inputValue;
  }

  /**
   * Gets HTML attributes for the input field
   */
  getHTMLAttributes() {
    return {
      min: `${this.constraints.minYear}-01-01`,
      max: `${this.constraints.maxYear}-12-31`,
      placeholder: 'mm/dd/yyyy'
    };
  }

  /**
   * Determines which field (day, month, year) the cursor is positioned in
   * For HTML5 date inputs in format YYYY-MM-DD
   */
  private getCursorFieldType(value: string, cursorPosition: number): 'day' | 'month' | 'year' | null {
    if (!value || value.length < 10) {
      // For incomplete dates, allow navigation on any position
      return 'year'; // Default to year for initialization
    }

    // HTML5 date input format: YYYY-MM-DD (positions 0-9)
    // Year: positions 0-3
    // Month: positions 5-6
    // Day: positions 8-9

    if (cursorPosition >= 0 && cursorPosition <= 4) {
      return 'year';
    } else if (cursorPosition >= 5 && cursorPosition <= 7) {
      return 'month';
    } else if (cursorPosition >= 8 && cursorPosition <= 10) {
      return 'day';
    }

    return null;
  }

  /**
   * Navigates a specific field (day, month, year) up or down
   */
  private navigateField(currentDate: Date, fieldType: 'day' | 'month' | 'year', key: string): Date | null {
    const newDate = new Date(currentDate);
    const isUp = key === 'ArrowUp';

    switch (fieldType) {
      case 'day':
        return this.navigateDay(newDate, isUp);
      case 'month':
        return this.navigateMonth(newDate, isUp);
      case 'year':
        return this.navigateYear(newDate, isUp);
      default:
        return null;
    }
  }

  /**
   * Navigate day field with proper month boundaries
   */
  private navigateDay(date: Date, isUp: boolean): Date {
    const currentDay = date.getDate();
    const currentMonth = date.getMonth();
    const currentYear = date.getFullYear();

    if (isUp) {
      // Get last day of current month
      const lastDayOfMonth = new Date(currentYear, currentMonth + 1, 0).getDate();
      const newDay = currentDay < lastDayOfMonth ? currentDay + 1 : 1;
      date.setDate(newDay);
    } else {
      const newDay = currentDay > 1 ? currentDay - 1 : new Date(currentYear, currentMonth + 1, 0).getDate();
      date.setDate(newDay);
    }

    return date;
  }

  /**
   * Navigate month field with year boundaries
   */
  private navigateMonth(date: Date, isUp: boolean): Date {
    const currentMonth = date.getMonth();

    if (isUp) {
      const newMonth = currentMonth < 11 ? currentMonth + 1 : 0;
      if (newMonth === 0) {
        // Moving to January of next year
        const newYear = Math.min(date.getFullYear() + 1, this.constraints.maxYear);
        date.setFullYear(newYear);
      }
      date.setMonth(newMonth);
    } else {
      const newMonth = currentMonth > 0 ? currentMonth - 1 : 11;
      if (newMonth === 11) {
        // Moving to December of previous year
        const newYear = Math.max(date.getFullYear() - 1, this.constraints.minYear);
        date.setFullYear(newYear);
      }
      date.setMonth(newMonth);
    }

    // Adjust day if it doesn't exist in the new month
    const lastDayOfNewMonth = new Date(date.getFullYear(), date.getMonth() + 1, 0).getDate();
    if (date.getDate() > lastDayOfNewMonth) {
      date.setDate(lastDayOfNewMonth);
    }

    return date;
  }

  /**
   * Navigate year field with constraints
   */
  private navigateYear(date: Date, isUp: boolean): Date {
    const currentYear = date.getFullYear();
    const newYear = isUp
      ? Math.min(currentYear + 1, this.constraints.maxYear)
      : Math.max(currentYear - 1, this.constraints.minYear);

    date.setFullYear(newYear);

    // Adjust day if it doesn't exist in the new year (Feb 29 edge case)
    const lastDayOfMonth = new Date(newYear, date.getMonth() + 1, 0).getDate();
    if (date.getDate() > lastDayOfMonth) {
      date.setDate(lastDayOfMonth);
    }

    return date;
  }

  /**
   * Parses input value to Date object
   */
  private parseInputValue(value: string): Date | null {
    if (!value) return null;
    
    const date = new Date(value);
    return isNaN(date.getTime()) ? null : date;
  }

  /**
   * Creates a date string with specified year (always January 1st)
   */
  private createDateStringWithYear(year: number): string {
    // Create date string directly to avoid timezone issues
    // Format: YYYY-01-01 (January 1st)
    return `${year.toString().padStart(4, '0')}-01-01`;
  }

  /**
   * Formats date for HTML date input (yyyy-mm-dd)
   */
  private formatDateForInput(date: Date): string {
    return date.toISOString().split('T')[0];
  }

  /**
   * Gets current constraints
   */
  getConstraints(): BirthDateConstraints {
    return { ...this.constraints };
  }
}
